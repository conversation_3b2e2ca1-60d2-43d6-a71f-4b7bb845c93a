<?php

namespace crmeb\services\payment\config;

/**
 * 分层支付配置字段定义
 * 定义基础层、服务商层、商户层的字段结构
 */
class LayeredPaymentFields
{
    /**
     * 直连配置层字段定义（商户自己的配置）
     */
    const DIRECT_FIELDS = [
        'wechat_direct' => [
            'appid' => ['type' => 'string', 'required' => true, 'label' => '应用ID'],
            'mch_id' => ['type' => 'string', 'required' => true, 'label' => '商户号'],
            'key' => ['type' => 'string', 'required' => true, 'label' => 'API密钥'],
            'api_v3_key' => ['type' => 'string', 'required' => false, 'label' => 'APIv3密钥'],
            'serial_no' => ['type' => 'string', 'required' => false, 'label' => '证书序列号'],
            'private_key' => ['type' => 'text', 'required' => false, 'label' => '商户私钥'],
            'public_key' => ['type' => 'text', 'required' => false, 'label' => '微信公钥'],
            'cert_path' => ['type' => 'string', 'required' => false, 'label' => '证书路径'],
            'key_path' => ['type' => 'string', 'required' => false, 'label' => '私钥路径']
        ],
        'alipay_direct' => [
            'app_id' => ['type' => 'string', 'required' => true, 'label' => '应用ID'],
            'private_key' => ['type' => 'text', 'required' => true, 'label' => '应用私钥'],
            'public_key' => ['type' => 'text', 'required' => true, 'label' => '支付宝公钥'],
            'cert_mode' => ['type' => 'boolean', 'required' => false, 'label' => '证书模式'],
            'app_cert' => ['type' => 'text', 'required' => false, 'label' => '应用证书'],
            'public_cert' => ['type' => 'text', 'required' => false, 'label' => '支付宝公钥证书'],
            'root_cert' => ['type' => 'text', 'required' => false, 'label' => '根证书']
        ],
        'balance_direct' => [],
        'offline_direct' => []
    ];
    
    /**
     * 服务商配置层字段定义（服务商的配置，商户不需要自己的基础配置）
     */
    const SERVICE_FIELDS = [
        'wechat_service' => [
            'service_appid' => ['type' => 'string', 'required' => true, 'label' => '服务商应用ID'],
            'service_mch_id' => ['type' => 'string', 'required' => true, 'label' => '服务商商户号'],
            'service_key' => ['type' => 'string', 'required' => false, 'label' => '服务商API密钥'],
            'service_v3_key' => ['type' => 'string', 'required' => true, 'label' => '服务商APIv3密钥'],
            'service_serial_no' => ['type' => 'string', 'required' => true, 'label' => '服务商证书序列号'],
            'service_private_key' => ['type' => 'text', 'required' => true, 'label' => '服务商私钥'],
            'service_public_key' => ['type' => 'text', 'required' => false, 'label' => '服务商公钥'],
            'service_cert_path' => ['type' => 'string', 'required' => false, 'label' => '服务商证书路径'],
            'service_key_path' => ['type' => 'string', 'required' => false, 'label' => '服务商私钥路径']
        ],
        'alipay_service' => [
            'service_app_id' => ['type' => 'string', 'required' => true, 'label' => '服务商应用ID'],
            'service_private_key' => ['type' => 'text', 'required' => true, 'label' => '服务商私钥'],
            'service_public_key' => ['type' => 'text', 'required' => true, 'label' => '服务商公钥'],
            'service_cert_mode' => ['type' => 'boolean', 'required' => false, 'label' => '服务商证书模式'],
            'service_app_cert' => ['type' => 'text', 'required' => false, 'label' => '服务商应用证书'],
            'service_public_cert' => ['type' => 'text', 'required' => false, 'label' => '服务商公钥证书'],
            'service_root_cert' => ['type' => 'text', 'required' => false, 'label' => '服务商根证书']
        ]
    ];
    
    /**
     * 商户配置层字段定义
     */
    const MERCHANT_FIELDS = [
        'wechat_merchant' => [
            'sub_mch_id' => ['type' => 'string', 'source' => 'merchant_table', 'label' => '子商户号'],
            'sub_appid' => ['type' => 'string', 'source' => 'merchant_config', 'label' => '子商户应用ID']
        ],
        'alipay_merchant' => [
            'alipay_smid' => ['type' => 'string', 'source' => 'merchant_table', 'label' => '支付宝SMID'],
            'app_auth_token' => ['type' => 'string', 'source' => 'auth_table', 'label' => '授权Token'],
            'auth_app_id' => ['type' => 'string', 'source' => 'auth_table', 'label' => '授权应用ID'],
            'refresh_token' => ['type' => 'string', 'source' => 'auth_table', 'label' => '刷新Token']
        ]
    ];
    
    /**
     * 系统配置映射（统一使用网关字段名作为key）
     */
    const SYSTEM_CONFIG_MAPPING = [
        'wechat_direct' => [
            'appid' => 'wechat_appid',
            'mch_id' => 'wechat_mchid',
            'key' => 'wechat_key',
            'api_v3_key' => 'wechat_api_v3_key',
            'serial_no' => 'wechat_serial_no',
            'private_key' => 'wechat_private_key',
            'public_key' => 'wechat_public_key',
            'cert_path' => 'wechat_cert_path',
            'key_path' => 'wechat_key_path'
        ],
        'alipay_direct' => [
            'app_id' => 'alipay_app_id',
            'private_key' => 'alipay_private_key',
            'public_key' => 'alipay_public_key',
            'cert_mode' => 'alipay_cert_mode',
            'app_cert' => 'alipay_app_cert',
            'public_cert' => 'alipay_public_cert',
            'root_cert' => 'alipay_root_cert'
        ],
        'wechat_service' => [
            'service_appid' => 'wechat_service_appid',
            'service_mch_id' => 'wechat_service_merid',
            'service_key' => 'wechat_service_key',
            'service_v3_key' => 'wechat_service_v3key',
            'service_serial_no' => 'wechat_service_serial_no',
            'service_private_key' => 'wechat_service_private_key',
            'service_public_key' => 'wechat_service_public_key',
            'service_cert_path' => 'wechat_service_client_cert',
            'service_key_path' => 'wechat_service_client_key'
        ],
        'alipay_service' => [
            'service_app_id' => 'alipay_service_app_id',
            'service_private_key' => 'alipay_service_private_key',
            'service_public_key' => 'alipay_service_public_key',
            'service_cert_mode' => 'alipay_service_cert_mode',
            'service_app_cert' => 'alipay_service_app_cert',
            'service_public_cert' => 'alipay_service_public_cert',
            'service_root_cert' => 'alipay_service_root_cert'
        ]
    ];
    
    /**
     * 获取指定层级的字段定义
     * @param string $layer 配置层级
     * @return array 字段定义
     */
    public static function getLayerFields(string $layer): array
    {
        switch ($layer) {
            case 'direct':
                return self::DIRECT_FIELDS;
            case 'service':
                return self::SERVICE_FIELDS;
            case 'merchant':
                return self::MERCHANT_FIELDS;
            default:
                return [];
        }
    }
    
    /**
     * 获取指定配置类型的字段定义
     * @param string $configType 配置类型
     * @return array 字段定义
     */
    public static function getConfigTypeFields(string $configType): array
    {
        // 先在直连字段中查找
        if (isset(self::DIRECT_FIELDS[$configType])) {
            return self::DIRECT_FIELDS[$configType];
        }

        // 再在服务商字段中查找
        if (isset(self::SERVICE_FIELDS[$configType])) {
            return self::SERVICE_FIELDS[$configType];
        }

        // 最后在商户字段中查找
        if (isset(self::MERCHANT_FIELDS[$configType])) {
            return self::MERCHANT_FIELDS[$configType];
        }

        return [];
    }
    
    /**
     * 获取系统配置映射
     * @param string $configType 配置类型
     * @return array 系统配置映射
     */
    public static function getSystemConfigMapping(string $configType): array
    {
        return self::SYSTEM_CONFIG_MAPPING[$configType] ?? [];
    }
    
    /**
     * 获取字段的必填状态
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return bool 是否必填
     */
    public static function isFieldRequired(string $configType, string $fieldName): bool
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['required'] ?? false;
    }
    
    /**
     * 获取字段类型
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return string 字段类型
     */
    public static function getFieldType(string $configType, string $fieldName): string
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['type'] ?? 'string';
    }
    
    /**
     * 获取字段标签
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return string 字段标签
     */
    public static function getFieldLabel(string $configType, string $fieldName): string
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['label'] ?? $fieldName;
    }
}
