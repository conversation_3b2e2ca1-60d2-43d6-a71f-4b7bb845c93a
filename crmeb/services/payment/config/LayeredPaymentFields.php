<?php

namespace crmeb\services\payment\config;

/**
 * 分层支付配置字段定义
 * 定义基础层、服务商层、商户层的字段结构
 */
class LayeredPaymentFields
{
    /**
     * 商户配置字段定义（商户在支付配置页面需要配置的字段）
     */
    const MERCHANT_CONFIG_FIELDS = [
        'wechat_direct' => [
            'appid' => ['type' => 'string', 'required' => true, 'label' => '应用ID'],
            'mch_id' => ['type' => 'string', 'required' => true, 'label' => '商户号'],
            'key' => ['type' => 'string', 'required' => true, 'label' => 'API密钥'],
            'api_v3_key' => ['type' => 'string', 'required' => false, 'label' => 'APIv3密钥'],
            'serial_no' => ['type' => 'string', 'required' => false, 'label' => '证书序列号'],
            'private_key' => ['type' => 'text', 'required' => false, 'label' => '商户私钥'],
            'public_key' => ['type' => 'text', 'required' => false, 'label' => '微信公钥'],
            'cert_path' => ['type' => 'string', 'required' => false, 'label' => '证书路径'],
            'key_path' => ['type' => 'string', 'required' => false, 'label' => '私钥路径']
        ],
        'alipay_direct' => [
            'app_id' => ['type' => 'string', 'required' => true, 'label' => '应用ID'],
            'private_key' => ['type' => 'text', 'required' => true, 'label' => '应用私钥'],
            'public_key' => ['type' => 'text', 'required' => true, 'label' => '支付宝公钥'],
            'cert_mode' => ['type' => 'boolean', 'required' => false, 'label' => '证书模式'],
            'app_cert' => ['type' => 'text', 'required' => false, 'label' => '应用证书'],
            'public_cert' => ['type' => 'text', 'required' => false, 'label' => '支付宝公钥证书'],
            'root_cert' => ['type' => 'text', 'required' => false, 'label' => '根证书']
        ],
        'wechat_merchant' => [
            'sub_mch_id' => ['type' => 'string', 'required' => true, 'label' => '子商户号'],
            'sub_appid' => ['type' => 'string', 'required' => false, 'label' => '子商户应用ID']
        ],
        'alipay_service' => [
            'app_auth_token' => ['type' => 'string', 'required' => true, 'label' => '商户授权令牌'],
            'auth_app_id' => ['type' => 'string', 'required' => false, 'label' => '授权应用ID'],
            'refresh_token' => ['type' => 'string', 'required' => false, 'label' => '刷新令牌']
        ],
        'alipay_merchant' => [
            'alipay_smid' => ['type' => 'string', 'required' => true, 'label' => '支付宝SMID（二级商户ID）'],
            'app_auth_token' => ['type' => 'string', 'required' => true, 'label' => '商户授权令牌'],
            'auth_app_id' => ['type' => 'string', 'required' => false, 'label' => '授权应用ID'],
            'refresh_token' => ['type' => 'string', 'required' => false, 'label' => '刷新令牌']
        ]
    ];
    

    
    /**
     * 系统配置映射（商户配置字段对应的系统配置键名）
     * 注意：这里只包含商户可能需要配置的字段，服务商配置在系统设置中管理
     */
    const SYSTEM_CONFIG_MAPPING = [
        'wechat_direct' => [
            'appid' => 'wechat_appid',
            'mch_id' => 'wechat_mchid',
            'key' => 'wechat_key',
            'api_v3_key' => 'wechat_api_v3_key',
            'serial_no' => 'wechat_serial_no',
            'private_key' => 'wechat_private_key',
            'public_key' => 'wechat_public_key',
            'cert_path' => 'wechat_cert_path',
            'key_path' => 'wechat_key_path'
        ],
        'alipay_direct' => [
            'app_id' => 'alipay_app_id',
            'private_key' => 'alipay_private_key',
            'public_key' => 'alipay_public_key',
            'cert_mode' => 'alipay_cert_mode',
            'app_cert' => 'alipay_app_cert',
            'public_cert' => 'alipay_public_cert',
            'root_cert' => 'alipay_root_cert'
        ]
        // 注意：服务商配置不在这里，在系统设置中管理
    ];
    
    /**
     * 获取指定层级的字段定义
     * @param string $layer 配置层级
     * @return array 字段定义
     */
    public static function getLayerFields(string $layer): array
    {
        switch ($layer) {
            case 'merchant':
                return self::MERCHANT_CONFIG_FIELDS;
            default:
                return [];
        }
    }
    
    /**
     * 获取指定配置类型的字段定义
     * @param string $configType 配置类型
     * @return array 字段定义
     */
    public static function getConfigTypeFields(string $configType): array
    {
        // 在商户配置字段中查找
        if (isset(self::MERCHANT_CONFIG_FIELDS[$configType])) {
            return self::MERCHANT_CONFIG_FIELDS[$configType];
        }

        return [];
    }
    
    /**
     * 获取系统配置映射
     * @param string $configType 配置类型
     * @return array 系统配置映射
     */
    public static function getSystemConfigMapping(string $configType): array
    {
        return self::SYSTEM_CONFIG_MAPPING[$configType] ?? [];
    }
    
    /**
     * 获取字段的必填状态
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return bool 是否必填
     */
    public static function isFieldRequired(string $configType, string $fieldName): bool
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['required'] ?? false;
    }
    
    /**
     * 获取字段类型
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return string 字段类型
     */
    public static function getFieldType(string $configType, string $fieldName): string
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['type'] ?? 'string';
    }
    
    /**
     * 获取字段标签
     * @param string $configType 配置类型
     * @param string $fieldName 字段名
     * @return string 字段标签
     */
    public static function getFieldLabel(string $configType, string $fieldName): string
    {
        $fields = self::getConfigTypeFields($configType);
        return $fields[$fieldName]['label'] ?? $fieldName;
    }
}
