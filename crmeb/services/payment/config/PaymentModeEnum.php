<?php

namespace crmeb\services\payment\config;

/**
 * 支付模式枚举
 * 定义所有支持的支付模式和配置层级关系
 */
class PaymentModeEnum
{
    // 微信支付模式
    const WECHAT_DIRECT = 'wechat_direct';           // 直连模式
    const WECHAT_SERVICE = 'wechat_service';         // 服务商模式
    const WECHAT_ECOMMERCE = 'wechat_ecommerce';     // 电商收付通
    
    // 支付宝支付模式
    const ALIPAY_DIRECT = 'alipay_direct';           // 直连模式
    const ALIPAY_SERVICE = 'alipay_service';         // 服务商模式
    const ALIPAY_ZHIFU = 'alipay_zhifu';            // 直付通模式
    
    // 其他支付模式
    const BALANCE_DIRECT = 'balance_direct';         // 余额支付
    const OFFLINE_DIRECT = 'offline_direct';         // 线下支付
    
    /**
     * 模式配置层级映射（商户配置页面需要的字段层级）
     * 注意：服务商配置在系统设置中管理，这里只定义商户需要配置的字段
     */
    const MODE_CONFIG_MAPPING = [
        // 微信直连：商户需要配置自己的微信支付信息
        self::WECHAT_DIRECT => [
            'merchant' => 'wechat_direct'
        ],
        // 微信服务商：商户只需要配置子商户号（服务商配置在系统设置中）
        self::WECHAT_SERVICE => [
            'merchant' => 'wechat_merchant'
        ],
        // 微信电商收付通：商户只需要配置子商户号（服务商配置在系统设置中）
        self::WECHAT_ECOMMERCE => [
            'merchant' => 'wechat_merchant'
        ],
        // 支付宝直连：商户需要配置自己的支付宝信息
        self::ALIPAY_DIRECT => [
            'merchant' => 'alipay_direct'
        ],
        // 支付宝服务商：商户可能不需要配置任何字段（服务商配置在系统设置中）
        self::ALIPAY_SERVICE => [
            // 可能为空，完全使用系统服务商配置
        ],
        // 支付宝直付通：商户需要配置授权信息（服务商配置在系统设置中）
        self::ALIPAY_ZHIFU => [
            'merchant' => 'alipay_merchant'
        ],
        // 余额支付：无需配置
        self::BALANCE_DIRECT => [
        ],
        // 线下支付：无需配置
        self::OFFLINE_DIRECT => [
        ]
    ];
    
    /**
     * 支付类型到模式的映射
     */
    const PAYMENT_TYPE_TO_MODE = [
        'wechat' => self::WECHAT_DIRECT,
        'weixin' => self::WECHAT_DIRECT,
        'weixinApp' => self::WECHAT_DIRECT,
        'routine' => self::WECHAT_DIRECT,
        'h5' => self::WECHAT_DIRECT,
        'alipay' => self::ALIPAY_DIRECT,
        'alipayApp' => self::ALIPAY_DIRECT,
        'balance' => self::BALANCE_DIRECT,
        'offline' => self::OFFLINE_DIRECT
    ];
    
    /**
     * 获取支付模式的配置层级
     * @param string $mode 支付模式
     * @return array 配置层级
     */
    public static function getConfigLayers(string $mode): array
    {
        return self::MODE_CONFIG_MAPPING[$mode] ?? [];
    }
    
    /**
     * 根据支付类型获取默认模式
     * @param string $paymentType 支付类型
     * @return string 支付模式
     */
    public static function getDefaultMode(string $paymentType): string
    {
        return self::PAYMENT_TYPE_TO_MODE[$paymentType] ?? self::WECHAT_DIRECT;
    }
    
    /**
     * 获取所有支持的支付模式
     * @return array 支付模式列表
     */
    public static function getAllModes(): array
    {
        return array_keys(self::MODE_CONFIG_MAPPING);
    }
    
    /**
     * 检查模式是否有效
     * @param string $mode 支付模式
     * @return bool 是否有效
     */
    public static function isValidMode(string $mode): bool
    {
        return isset(self::MODE_CONFIG_MAPPING[$mode]);
    }
    
    /**
     * 获取模式显示名称
     * @param string $mode 支付模式
     * @return string 显示名称
     */
    public static function getModeDisplayName(string $mode): string
    {
        $names = [
            self::WECHAT_DIRECT => '微信直连支付',
            self::WECHAT_SERVICE => '微信服务商支付',
            self::WECHAT_ECOMMERCE => '微信电商收付通',
            self::ALIPAY_DIRECT => '支付宝直连支付',
            self::ALIPAY_SERVICE => '支付宝服务商支付',
            self::ALIPAY_ZHIFU => '支付宝直付通',
            self::BALANCE_DIRECT => '余额支付',
            self::OFFLINE_DIRECT => '线下支付'
        ];
        
        return $names[$mode] ?? $mode;
    }
}
