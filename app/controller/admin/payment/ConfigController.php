<?php

namespace app\controller\admin\payment;

use crmeb\basic\BaseController;
use crmeb\services\payment\config\PaymentConfigCenter;
use crmeb\services\payment\config\LayeredConfigResolver;
use crmeb\services\payment\config\PaymentModeEnum;
use crmeb\services\payment\config\LayeredPaymentFields;
use think\App;
use think\facade\Db;

/**
 * 支付配置管理控制器 - 基于分层架构的简化版本
 */
class ConfigController extends BaseController
{
    public function __construct(App $app)
    {
        parent::__construct($app);
    }
    
    /**
     * 获取支付配置（简化版本）
     */
    public function getPaymentConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            
            if (empty($merId)) {
               // return $this->fail('商户ID不能为空');
            }
            
            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }
            
            // 使用重构后的配置中心
            $config = PaymentConfigCenter::getEffectiveConfig($merId, $paymentType);
            $template = PaymentConfigCenter::getConfigTemplate($paymentType, $merId);
            $isComplete = PaymentConfigCenter::isConfigComplete($paymentType, $config ?: [], $merId);
            
            // 获取支付模式信息
            $resolver = new LayeredConfigResolver();
            $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            $modeInfo = [
                'payment_mode' => $paymentMode,
                'mode_name' => PaymentModeEnum::getModeDisplayName($paymentMode),
                'config_layers' => PaymentModeEnum::getConfigLayers($paymentMode)
            ];
            
            return $this->success([
                'config' => $config,
                'template' => $template,
                'is_complete' => $isComplete,
                'mode_info' => $modeInfo,
                'mer_id' => $merId,
                'payment_type' => $paymentType
            ]);
            
        } catch (\Exception $e) {
            return $this->fail('获取支付配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 创建支付配置
     */
    public function createPaymentConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            $configData = $this->request->param('config', []);
            $status = $this->request->param('status', 'active');
            $paymentMode = $this->request->param('payment_mode', null); // 前端选择的支付模式

            if (empty($merId)) {
                return $this->fail('商户ID不能为空');
            }

            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }

            // 检查是否已存在配置
            $existingConfig = Db::name('merchant_payment_config')
                ->where('mer_id', $merId)
                ->where('payment_type', $paymentType)
                ->find();

            if ($existingConfig) {
                return $this->fail('该商户的此支付方式配置已存在，请使用编辑功能');
            }

            // 验证配置数据（使用用户选择的支付模式）
            if (!empty($configData)) {
                $validation = $this->validateConfigData($paymentType, $configData, $merId, $paymentMode);
                if (!$validation['valid']) {
                    return $this->fail('配置验证失败: ' . implode(', ', $validation['errors']));
                }
            }

            // 保存配置
            $result = $this->saveMerchantConfig($merId, $paymentType, $configData, $status, $paymentMode);

            if ($result) {
                return $this->success([
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'payment_mode' => $paymentMode,
                    'status' => $status
                ], '配置创建成功');
            } else {
                return $this->fail('配置创建失败');
            }

        } catch (\Exception $e) {
            return $this->fail('创建支付配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 更新支付配置（简化版本）
     */
    public function updatePaymentConfig()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            $configData = $this->request->param('config', []);
            $status = $this->request->param('status', 'active');
            $paymentMode = $this->request->param('payment_mode', null); // 前端选择的支付模式

            if (empty($merId)) {
                return $this->fail('商户ID不能为空');
            }

            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }

            // 验证配置数据（使用用户选择的支付模式）
            if (!empty($configData)) {
                $validation = $this->validateConfigData($paymentType, $configData, $merId, $paymentMode);
                if (!$validation['valid']) {
                    return $this->fail('配置验证失败: ' . implode(', ', $validation['errors']));
                }
            }

            // 保存配置
            $result = $this->saveMerchantConfig($merId, $paymentType, $configData, $status, $paymentMode);

            if ($result) {
                // 清除缓存
                PaymentConfigCenter::clearCache($merId, $paymentType);

                return $this->success([
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'payment_mode' => $paymentMode,
                    'updated_fields' => array_keys($configData)
                ], '配置更新成功');
            } else {
                return $this->fail('配置更新失败');
            }

        } catch (\Exception $e) {
            return $this->fail('更新支付配置失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取支付模式信息
     */
    public function getPaymentModes()
    {
        try {
            $merId = $this->request->param('mer_id', 0);
            $paymentType = $this->request->param('payment_type', '');
            
            if (!empty($merId) && !empty($paymentType)) {
                // 获取特定商户和支付类型的模式信息
                $resolver = new LayeredConfigResolver();
                $paymentMode = $resolver->determinePaymentMode($merId, $paymentType);
                $modeInfo = [
                    'payment_mode' => $paymentMode,
                    'mode_name' => PaymentModeEnum::getModeDisplayName($paymentMode),
                    'config_layers' => PaymentModeEnum::getConfigLayers($paymentMode),
                    'is_service_mode' => in_array($paymentMode, [
                        PaymentModeEnum::WECHAT_SERVICE,
                        PaymentModeEnum::WECHAT_ECOMMERCE,
                        PaymentModeEnum::ALIPAY_SERVICE,
                        PaymentModeEnum::ALIPAY_ZHIFU
                    ])
                ];
                return $this->success($modeInfo);
            } else {
                // 获取所有支持的支付模式
                $modes = [];
                foreach (PaymentModeEnum::getAllModes() as $mode) {
                    $modes[$mode] = [
                        'mode' => $mode,
                        'name' => PaymentModeEnum::getModeDisplayName($mode),
                        'layers' => PaymentModeEnum::getConfigLayers($mode)
                    ];
                }
                
                return $this->success([
                    'modes' => $modes,
                    'payment_types' => array_keys(PaymentModeEnum::PAYMENT_TYPE_TO_MODE)
                ]);
            }
            
        } catch (\Exception $e) {
            return $this->fail('获取支付模式失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取配置模板
     */
    public function getConfigTemplate()
    {
        try {
            $paymentType = $this->request->param('payment_type', '');
            $merId = $this->request->param('mer_id', 0);
            $paymentMode = $this->request->param('payment_mode', null);

            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }

            $template = PaymentConfigCenter::getConfigTemplate($paymentType, $merId, $paymentMode);

            // 如果指定了支付模式，使用指定的模式信息；否则使用自动确定的模式
            if ($paymentMode) {
                $modeInfo = [
                    'payment_mode' => $paymentMode,
                    'mode_name' => PaymentModeEnum::getModeDisplayName($paymentMode),
                    'config_layers' => PaymentModeEnum::getConfigLayers($paymentMode),
                    'is_service_mode' => in_array($paymentMode, [
                        PaymentModeEnum::WECHAT_SERVICE,
                        PaymentModeEnum::WECHAT_ECOMMERCE,
                        PaymentModeEnum::ALIPAY_SERVICE,
                        PaymentModeEnum::ALIPAY_ZHIFU
                    ])
                ];
            } else {
                $modeInfo = PaymentConfigCenter::getPaymentModeInfo($merId, $paymentType);
            }

            $availableModes = PaymentConfigCenter::getAvailableModesForPaymentType($paymentType);

            return $this->success([
                'payment_type' => $paymentType,
                'template' => $template,
                'mode_info' => $modeInfo,
                'available_modes' => $availableModes
            ]);

        } catch (\Exception $e) {
            return $this->fail('获取配置模板失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 验证配置数据
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @param int $merId 商户ID
     * @param string|null $paymentMode 用户选择的支付模式
     * @return array
     */
    private function validateConfigData(string $paymentType, array $configData, int $merId, ?string $paymentMode = null): array
    {
        $errors = [];

        try {
            // 如果用户指定了支付模式，使用指定的模式；否则自动确定
            if ($paymentMode) {
                $actualPaymentMode = $paymentMode;
            } else {
                $resolver = new LayeredConfigResolver();
                $actualPaymentMode = $resolver->determinePaymentMode($merId, $paymentType);
            }

            $configLayers = PaymentModeEnum::getConfigLayers($actualPaymentMode);

            // 验证必填字段
            foreach ($configLayers as $layer => $configType) {
                $fields = LayeredPaymentFields::getConfigTypeFields($configType);

                foreach ($fields as $fieldName => $fieldDef) {
                    // 跳过有source属性的字段（这些字段由系统自动获取）
                    if (isset($fieldDef['source'])) {
                        continue;
                    }

                    if ($fieldDef['required'] && empty($configData[$fieldName])) {
                        $errors[] = "缺少必填字段: {$fieldDef['label']}({$fieldName})";
                    }
                }
            }

        } catch (\Exception $e) {
            $errors[] = "验证过程出错: " . $e->getMessage();
        }

        return ['valid' => empty($errors), 'errors' => $errors];
    }
    
    /**
     * 保存商户配置
     * @param int $merId 商户ID
     * @param string $paymentType 支付类型
     * @param array $configData 配置数据
     * @param string $status 状态
     * @param string|null $paymentMode 支付模式
     * @return bool
     */
    private function saveMerchantConfig(int $merId, string $paymentType, array $configData, string $status = 'active', ?string $paymentMode = null): bool
    {
        try {
            Db::startTrans();
            
            // 查找或创建配置记录
            $configRecord = Db::name('merchant_payment_config')
                ->where('mer_id', $merId)
                ->where('payment_type', $paymentType)
                ->find();
            
            if (!$configRecord) {
                $configId = Db::name('merchant_payment_config')->insertGetId([
                    'mer_id' => $merId,
                    'payment_type' => $paymentType,
                    'payment_mode' => $paymentMode, // 保存用户选择的支付模式
                    'status' => $status === 'active' ? 1 : 0,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            } else {
                $configId = $configRecord['id'];
                $updateData = [
                    'status' => $status === 'active' ? 1 : 0,
                    'update_time' => time()
                ];

                // 如果指定了支付模式，更新支付模式
                if ($paymentMode) {
                    $updateData['payment_mode'] = $paymentMode;
                }

                Db::name('merchant_payment_config')
                    ->where('id', $configId)
                    ->update($updateData);
            }
            
            // 删除旧参数
            Db::name('merchant_payment_params')
                ->where('mer_id', $merId)
                ->where('payment_id', $configId)
                ->delete();
            
            // 插入新参数
            $params = [];
            foreach ($configData as $key => $value) {
                if (!empty($value)) {
                    $params[] = [
                        'mer_id' => $merId,
                        'payment_id' => $configId,
                        'param_name' => $key,
                        'param_value' => is_array($value) ? json_encode($value) : $value,
                        'create_time' => time()
                    ];
                }
            }
            
            if (!empty($params)) {
                Db::name('merchant_payment_params')->insertAll($params);
            }
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 验证配置
     */
    public function validateConfig()
    {
        try {
            $paymentType = $this->request->param('payment_type', '');
            $config = $this->request->param('config', []);
            $merId = $this->request->param('mer_id', 0);

            if (empty($paymentType)) {
                return $this->fail('支付类型不能为空');
            }

            $isValid = PaymentConfigCenter::validateConfig($paymentType, $config, $merId);

            return $this->success([
                'valid' => $isValid,
                'payment_type' => $paymentType,
                'mer_id' => $merId
            ]);

        } catch (\Exception $e) {
            return $this->fail('验证配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 清除配置缓存
     */
    public function clearCache()
    {
        try {
            $merId = $this->request->param('mer_id');
            $paymentType = $this->request->param('payment_type');

            PaymentConfigCenter::clearCache($merId, $paymentType);

            return $this->success([], '缓存清除成功');

        } catch (\Exception $e) {
            return $this->fail('清除缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取商户配置列表
     */
    public function getMerchantConfigs()
    {
        try {
            $page = $this->request->param('page', 1);
            $limit = $this->request->param('limit', 20);
            $keyword = $this->request->param('keyword', '');
            $paymentType = $this->request->param('payment_type', '');

            $where = [];
            if (!empty($keyword)) {
                $where[] = ['mer_name', 'like', "%{$keyword}%"];
            }
            if (!empty($paymentType)) {
                $where[] = ['payment_type', '=', $paymentType];
            }

            $query = Db::name('merchant_payment_config')
                ->alias('mpc')
                ->leftJoin('merchant m', 'mpc.mer_id = m.mer_id')
                ->field('mpc.*, m.mer_name as merchant_name')
                ->where($where)
                ->order('mpc.update_time DESC');

            $total = $query->count();
            $list = $query->page($page, $limit)->select()->toArray();

            // 获取配置参数
            foreach ($list as &$item) {
                $params = Db::name('merchant_payment_params')
                    ->where('payment_id', $item['id'])
                    ->column('param_value', 'param_name');
                $item['config'] = $params;
            }

            return $this->success([
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return $this->fail('获取商户配置失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取支付方式列表
     */
    public function getPaymentMethods()
    {
        try {
            $methods = [
                ['value' => 'wechat', 'label' => '微信支付'],
                ['value' => 'alipay', 'label' => '支付宝'],
                ['value' => 'routine', 'label' => '小程序支付'],
                ['value' => 'balance', 'label' => '余额支付'],
                ['value' => 'offline', 'label' => '线下支付']
            ];

            return $this->success($methods);

        } catch (\Exception $e) {
            return $this->fail('获取支付方式失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取商户列表
     */
    public function getMerchantList()
    {
        try {
            $keyword = $this->request->param('keyword', '');
            $limit = $this->request->param('limit', 100);

            $where = [];
            if (!empty($keyword)) {
                $where[] = ['mer_name', 'like', "%{$keyword}%"];
            }

            $merchants = Db::name('merchant')
                ->field('mer_id, mer_name, mer_phone, status')
                ->where($where)
                ->where('status', 1) // 只获取启用的商户
                ->limit($limit)
                ->order('mer_id ASC')
                ->select()
                ->toArray();

            return $this->success($merchants);

        } catch (\Exception $e) {
            return $this->fail('获取商户列表失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除支付配置
     */
    public function deletePaymentConfig()
    {
        try {
            // 从路由参数获取
            $configType = $this->request->param('type', 'merchant');
            $configId = $this->request->param('id', 0);

            if (empty($configId)) {
                return $this->fail('配置ID不能为空');
            }

            // 获取配置信息
            $config = Db::name('merchant_payment_config')
                ->where('id', $configId)
                ->find();

            if (!$config) {
                return $this->fail('配置不存在');
            }

            Db::startTrans();

            try {
                // 删除配置参数
                Db::name('merchant_payment_params')
                    ->where('payment_id', $configId)
                    ->delete();

                // 删除配置记录
                Db::name('merchant_payment_config')
                    ->where('id', $configId)
                    ->delete();

                // 清除缓存
                PaymentConfigCenter::clearCache($config['mer_id'], $config['payment_type']);

                Db::commit();

                return $this->success([], '配置删除成功');

            } catch (\Exception $e) {
                Db::rollback();
                throw $e;
            }

        } catch (\Exception $e) {
            return $this->fail('删除配置失败: ' . $e->getMessage());
        }
    }
}
