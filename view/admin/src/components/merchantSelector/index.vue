<template>
  <div class="merchant-selector">
    <!-- 商户选择输入框 -->
    <el-input
      :value="displayText"
      :placeholder="placeholder"
      readonly
      :disabled="disabled"
      @click.native="showDialog"
    >
      <el-button 
        slot="append" 
        icon="el-icon-search" 
        :disabled="disabled"
        @click="showDialog"
      ></el-button>
    </el-input>

    <!-- 商户选择对话框 -->
    <el-dialog
      title="选择商户"
      :visible.sync="dialogVisible"
      width="650px"
      :append-to-body="true"
      @close="handleClose"
    >
      <!-- 搜索框 -->
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="输入商户名称或ID搜索"
          clearable
          style="width: 250px; margin-right: 10px;"
          @input="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>
        <el-button @click="refreshMerchants" icon="el-icon-refresh">刷新</el-button>
      </div>

      <!-- 商户列表 -->
      <el-table
        v-loading="loading"
        :data="filteredMerchants"
        height="400"
        size="small"
        highlight-current-row
        stripe
        style="width: 100%; margin-top: 15px;"
        border
        @row-click="handleSelect"
      >
        <el-table-column prop="mer_id" label="商户ID" min-width="100" />
        <el-table-column prop="mer_name" label="商户名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="mer_phone" label="联系电话" min-width="120" />
        <el-table-column label="状态" min-width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="mini">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="80">
          <template slot-scope="scope">
            <el-button 
              type="primary" 
              size="mini" 
              @click.stop="handleSelect(scope.row)"
            >
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalMerchants"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange"
        />
      </div>

      <div slot="footer" class="text-center">
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantListApi } from '@/api/payment'

export default {
  name: 'MerchantSelector',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: '点击选择商户'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      searchKeyword: '',
      merchants: [],
      filteredMerchants: [],
      selectedMerchant: null,
      currentPage: 1,
      pageSize: 10,
      totalMerchants: 0
    }
  },
  computed: {
    displayText() {
      if (this.selectedMerchant) {
        return this.selectedMerchant.mer_name
      }
      return this.value ? `商户ID: ${this.value}` : ''
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && !this.selectedMerchant) {
          this.findMerchantById(newVal)
        }
      },
      immediate: true
    }
  },
  methods: {
    showDialog() {
      if (this.disabled) return
      this.dialogVisible = true
      this.loadMerchants()
    },

    async loadMerchants() {
      this.loading = true
      try {
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
          keyword: this.searchKeyword
        }
        const res = await getMerchantListApi(params)
        this.merchants = res.data || []
        this.totalMerchants = this.merchants.length
        this.filterMerchants()
      } catch (error) {
        console.error('获取商户列表失败:', error)
        this.$message.error('获取商户列表失败')
        this.merchants = []
        this.filteredMerchants = []
      } finally {
        this.loading = false
      }
    },

    filterMerchants() {
      if (!this.searchKeyword) {
        this.filteredMerchants = this.merchants
      } else {
        this.filteredMerchants = this.merchants.filter(merchant => 
          merchant.mer_name.includes(this.searchKeyword) ||
          merchant.mer_id.toString().includes(this.searchKeyword)
        )
      }
    },

    handleSearch() {
      this.currentPage = 1
      this.filterMerchants()
    },

    handlePageChange(page) {
      this.currentPage = page
      this.loadMerchants()
    },

    refreshMerchants() {
      this.searchKeyword = ''
      this.currentPage = 1
      this.loadMerchants()
    },

    handleSelect(merchant) {
      this.selectedMerchant = merchant
      this.$emit('input', merchant.mer_id)
      this.$emit('change', merchant)
      this.dialogVisible = false
    },

    handleClose() {
      this.searchKeyword = ''
    },

    findMerchantById(merchantId) {
      const merchant = this.merchants.find(m => m.mer_id == merchantId)
      if (merchant) {
        this.selectedMerchant = merchant
      }
    }
  }
}
</script>

<style scoped>
.search-box {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.pagination-wrapper {
  text-align: center;
  margin-top: 15px;
}

.text-center {
  text-align: center;
}
</style>
