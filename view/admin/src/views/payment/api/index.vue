<template>
  <div class="api-payment">
    <el-card>
      <el-row type="flex" class="mb-3">
        <el-col :span="24">
          <el-form ref="searchForm" :model="searchForm" inline @submit.native.prevent>
            <el-form-item label="商户：">
              <MerchantSelector
                v-model="searchForm.merchant_id"
                placeholder="点击选择商户"
                style="width:250px;"
                @change="handleMerchantChange"
              />
            </el-form-item>
            <el-form-item label="支付状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option value="0" label="未支付"></el-option>
                <el-option value="1" label="已支付"></el-option>
                <el-option value="2" label="已退款"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select v-model="searchForm.search_type" placeholder="搜索类型" style="width: 120px;" clearable>
                <el-option value="order_sn" label="订单号"></el-option>
                <el-option value="transaction_id" label="交易号"></el-option>
                <el-option value="merchant_name" label="商户名称"></el-option>
                <el-option value="partner_name" label="合作伙伴"></el-option>
                <el-option value="out_trade_no" label="合作伙伴订单号"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="searchForm.keyword" style="width: 200px" placeholder="请输入关键词" clearable></el-input>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="searchForm.date"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 240px">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetSearch">重置</el-button>
              <el-button type="success" @click="handleExport">导出</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-3">
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-content">
              <div class="statistic-title">今日交易额</div>
              <div class="statistic-value">
                <count-to :custom-style="{fontSize: '24px', fontWeight: 'bold', color: '#409EFF'}" :end-val="statistics.todayAmount" :decimals="2" prefix="¥"></count-to>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-content">
              <div class="statistic-title">今日订单数</div>
              <div class="statistic-value">
                <count-to :custom-style="{fontSize: '24px', fontWeight: 'bold', color: '#67C23A'}" :end-val="statistics.todayCount"></count-to>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-content">
              <div class="statistic-title">总交易额</div>
              <div class="statistic-value">
                <count-to :custom-style="{fontSize: '24px', fontWeight: 'bold', color: '#E6A23C'}" :end-val="statistics.totalAmount" :decimals="2" prefix="¥"></count-to>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="statistic-card">
            <div class="statistic-content">
              <div class="statistic-title">总订单数</div>
              <div class="statistic-value">
                <count-to :custom-style="{fontSize: '24px', fontWeight: 'bold', color: '#F56C6C'}" :end-val="statistics.totalCount"></count-to>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card>
      <el-table v-loading="loading" :data="tableData" border stripe>
        <el-table-column type="expand" width="50">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-form-item label="订单ID:">
                <span>{{ props.row.order_id }}</span>
              </el-form-item>
              <el-form-item label="商户类型:">
                <span>{{ props.row.partner_name }}</span>
              </el-form-item>
              <el-form-item label="通知响应:">
                <span>{{ props.row.notify_response || '无响应' }}</span>
              </el-form-item>
              <el-form-item label="重试次数:">
                <span>{{ props.row.retry_count }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商户名称" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="merchant-info">
              <el-tag 
                v-if="scope.row.merchant_type === '合作伙伴'" 
                type="success" 
                size="mini"
                class="mb-1"
              >
                合作伙伴
              </el-tag>
              <el-tag 
                v-else 
                type="info" 
                size="mini"
                class="mb-1"
              >
                直营商户
              </el-tag>
              <div class="merchant-name">{{ scope.row.display_name }}</div>
              <div v-if="scope.row.merchant_type === '合作伙伴' && scope.row.partner_code" class="partner-code">
                {{ scope.row.partner_code }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order_sn" label="订单号" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button type="text" @click="copyText(scope.row.order_sn)" class="copy-btn">
              {{ scope.row.order_sn }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="partner_order_id" label="合作伙伴订单号" width="180" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button v-if="scope.row.partner_order_id" type="text" @click="copyText(scope.row.partner_order_id)" class="copy-btn">
              {{ scope.row.partner_order_id }}
            </el-button>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="支付金额" width="120">
          <template slot-scope="scope">
            <span class="amount">¥{{ scope.row.amount }}</span>
            <div v-if="scope.row.is_refunded" class="refund-info">
              <el-tag type="danger" size="mini">已退款 ¥{{ scope.row.refund_amount }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="pay_type_name" label="支付方式" width="120">
          <template slot-scope="scope">
            <el-tag 
              :type="getPaymentTypeTag(scope.row.pay_type_name)" 
              size="small"
            >
              {{ scope.row.pay_type_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status_name" label="支付状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.paid, scope.row.status)" size="small">
              {{ scope.row.status_name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="pay_time" label="支付时间" width="160" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.pay_time">{{ scope.row.pay_time }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="transaction_id" label="交易号" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button 
              v-if="scope.row.transaction_id" 
              type="text" 
              @click="copyText(scope.row.transaction_id)" 
              class="copy-btn"
            >
              {{ scope.row.transaction_id }}
            </el-button>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="notify_status_name" label="通知状态" width="120">
          <template slot-scope="scope">
            <el-tag :type="getNotifyStatusType(scope.row.notify_status)" size="small">
              {{ scope.row.notify_status_name || '未通知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="handleDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </el-card>



    <!-- 详情弹窗 -->
    <el-dialog
      :title="'订单详情 - ' + (detailData.order_sn || '')"
      :visible.sync="detailVisible"
      @close="closeDetail"
      width="850px"
      center
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单ID">{{ detailData.order_id }}</el-descriptions-item>
        <el-descriptions-item label="订单号">{{ detailData.order_sn }}</el-descriptions-item>
        
        <!-- 添加合作伙伴订单号显示 -->
        <el-descriptions-item label="合作伙伴订单号">
          {{ detailData.partner_order_id || '-' }}
        </el-descriptions-item>
        
        <!-- 商户信息 -->
        <el-descriptions-item label="商户类型">
          <el-tag 
            :type="detailData.merchant_type === '合作伙伴' ? 'success' : 'info'" 
            size="small"
          >
            {{ detailData.merchant_type }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="商户名称">{{ detailData.display_name }}</el-descriptions-item>
        
        <el-descriptions-item v-if="detailData.merchant_type === '合作伙伴'" label="合作伙伴编码">
          {{ detailData.partner_code || '-' }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.merchant_phone" label="商户电话">
          {{ detailData.merchant_phone }}
        </el-descriptions-item>
        
        <!-- 支付信息 -->
        <el-descriptions-item label="支付金额">
          <span class="amount">¥{{ detailData.amount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <el-tag :type="getPaymentTypeTag(detailData.pay_type_name)" size="small">
            {{ detailData.pay_type_name }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="支付状态">
          <el-tag :type="getStatusType(detailData.paid, detailData.status)" size="small">
            {{ detailData.status_name }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="交易号">{{ detailData.transaction_id || '-' }}</el-descriptions-item>
        
        <!-- 时间信息 -->
        <el-descriptions-item label="创建时间">{{ detailData.create_time }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ detailData.pay_time || '-' }}</el-descriptions-item>
        
        <!-- 退款信息和退款按钮 -->
        <el-descriptions-item label="退款状态" :span="detailData.is_refunded ? 1 : 2">
          <template v-if="detailData.is_refunded">
            <el-tag type="danger" size="small">已退款</el-tag>
          </template>
          <template v-else-if="detailData.paid == 1">
            <el-tag type="info" size="small">未退款</el-tag>
            <el-button 
              class="ml-2"
              type="danger" 
              size="mini"
              @click="handleRefund(detailData)"
            >
              <i class="el-icon-back"></i> 退款
            </el-button>
          </template>
          <template v-else>
            <el-tag type="warning" size="small">未支付</el-tag>
          </template>
        </el-descriptions-item>
        
        <!-- 仅当已退款时显示退款金额和退款时间 -->
        <el-descriptions-item v-if="detailData.is_refunded" label="退款金额">
          <span class="refund-amount">¥{{ detailData.refund_amount }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item v-if="detailData.is_refunded" label="退款时间">
          {{ detailData.refund_time }}
        </el-descriptions-item>
        
        <!-- 通知信息 -->
        <el-descriptions-item label="通知状态">
          <el-tag :type="getNotifyStatusType(detailData.notify_status)" size="small">
            {{ detailData.notify_status_name }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="重试次数">{{ detailData.retry_count }}</el-descriptions-item>
        
        <el-descriptions-item v-if="detailData.notify_url" label="通知地址">
          {{ detailData.notify_url }}
        </el-descriptions-item>
        <el-descriptions-item v-if="detailData.notify_time" label="通知时间">
          {{ detailData.notify_time }}
        </el-descriptions-item>
        
        <el-descriptions-item label="通知响应" :span="2">
          <el-input
            type="textarea"
            :rows="3"
            :value="detailData.notify_response || '无响应'"
            readonly
          />
        </el-descriptions-item>
      </el-descriptions>
      
      <!-- 详情操作区域 -->
      <div style="margin-top: 20px; text-align: center;">
        <!-- 导出功能 -->
        <el-button-group>
          <el-button 
            type="success" 
            @click="handleExportSingle(detailData)"
          >
            <i class="el-icon-download"></i> 导出订单
          </el-button>
        </el-button-group>

        <!-- 根据订单状态显示不同的操作按钮 -->
        <el-button-group class="ml-2" v-if="detailData.paid == 0">
          <!-- 未支付订单操作 -->
          <el-button 
            type="primary" 
            @click="handleSync(detailData)"
          >
            <i class="el-icon-refresh"></i> 同步状态
          </el-button>
          <el-button 
            type="danger" 
            @click="handleDelete(detailData)"
          >
            <i class="el-icon-delete"></i> 删除订单
          </el-button>
        </el-button-group>

        <!-- 通知功能，所有订单都显示 -->
        <el-button-group class="ml-2">
          <el-button 
            type="warning" 
            @click="handleNotify(detailData)"
          >
            <i class="el-icon-message"></i> 发送通知
          </el-button>
        </el-button-group>

        <!-- 复制功能按钮组，无论订单状态 -->
        <el-button-group class="ml-2">
          <el-button 
            type="info" 
            @click="copyText(detailData.order_sn)"
          >
            <i class="el-icon-copy-document"></i> 复制订单号
          </el-button>
          <el-button 
            v-if="detailData.transaction_id" 
            type="info" 
            @click="copyText(detailData.transaction_id)"
          >
            <i class="el-icon-copy-document"></i> 复制交易号
          </el-button>
          <el-button 
            v-if="detailData.partner_order_id" 
            type="info" 
            @click="copyText(detailData.partner_order_id)"
          >
            <i class="el-icon-copy-document"></i> 复制合作伙伴订单号
          </el-button>
        </el-button-group>
      </div>
      
      <div slot="footer" class="text-center">
        <el-button @click="closeDetail">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApiPaymentList, getApiPaymentStatistics, exportApiPaymentData, syncOrderStatus, sendApiNotification, deleteApiOrder, refundApiOrder } from '@/api/payment'
import CountTo from '@/components/countTo'
import MerchantSelector from '@/components/merchantSelector/index.vue'

export default {
  name: 'ApiPayment',
  components: {
    CountTo,
    MerchantSelector
  },
  data() {
    return {
      loading: false,
      detailVisible: false,
      searchForm: {
        merchant_id: '',
        status: '',
        date: [],
        search_type: '',
        keyword: ''
      },
      
      tableData: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      statistics: {
        todayAmount: 0,
        todayCount: 0,
        totalAmount: 0,
        totalCount: 0
      },
      detailData: {}
    }
  },
  mounted() {
    this.getList()
    this.getStatistics()
  },
  methods: {
    // 商户变化处理
    handleMerchantChange(merchant) {
      // 商户选择后自动搜索
      this.handleSearch()
    },

    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit,
          merchant_id: this.searchForm.merchant_id,
          status: this.searchForm.status,
          date: this.searchForm.date,
          search_type: this.searchForm.search_type,
          keyword: this.searchForm.keyword
        }
        const { data } = await getApiPaymentList(params)
        if (data && data.list) {
          this.tableData = data.list
          this.pagination.total = data.count || 0
        } else {
          this.tableData = []
          this.pagination.total = 0
          this.$message.warning('未获取到数据')
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
        this.tableData = []
      } finally {
        this.loading = false
      }
    },

    // 获取统计数据
    async getStatistics() {
      try {
        const { data } = await getApiPaymentStatistics()
        // 确保所有数值字段都有默认值，防止undefined导致NaN错误
        this.statistics = {
          todayAmount: parseFloat(data.today_amount || 0),
          todayCount: parseInt(data.today_count || 0),
          totalAmount: parseFloat(data.total_amount || 0),
          totalCount: parseInt(data.total_count || 0)
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
        // 发生错误时设置默认值
        this.statistics = {
          todayAmount: 0,
          todayCount: 0,
          totalAmount: 0,
          totalCount: 0
        }
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.getList()
    },

    // 重置
    resetSearch() {
      this.searchForm = {
        merchant_id: '',
        status: '',
        date: [],
        search_type: '',
        keyword: ''
      }
      this.pagination.page = 1
      this.getList()
    },

    // 导出
    async handleExport() {
      try {
        this.$message.info('正在导出数据，请稍候...')
        const params = { ...this.searchForm }
        const { data } = await exportApiPaymentData(params)
        
        // 检查返回的数据
        if (!data) {
          this.$message.error('导出失败 - 未获取到文件URL')
          return
        }
        
        // 直接打开下载链接
        const baseUrl = window.location.origin
        window.open(baseUrl + data, '_blank')
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      }
    },

    // 查看详情
    handleDetail(row) {
      this.detailData = { ...row }
      this.detailVisible = true
    },

    // 同步订单状态
    async handleSync(row) {
      try {
        this.$message.info('正在同步订单状态...')
        const { data } = await syncOrderStatus(row.order_id)
        this.$message.success(data || '同步完成')
        this.getList()
      } catch (error) {
        this.$message.error('同步失败')
      }
    },

    // 获取状态类型
    getStatusType(paid, status) {
      if (paid == 1) return 'success'
      if (status == -1) return 'danger'
      return 'warning'
    },

    // 获取通知状态类型
    getNotifyStatusType(status) {
      if (status == 1) return 'success'
      if (status == 0) return 'danger'
      return 'warning'
    },

    // 发送通知
    async handleNotify(row) {
      try {
        this.$confirm('确定要发送通知给合作伙伴吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.$message.info('正在发送通知...')
          const { data } = await sendApiNotification({ order_id: row.order_id })
          this.$message.success(data || '通知发送成功')
          this.getList()
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('发送通知失败')
        }
      }
    },

    // 删除订单
    async handleDelete(row) {
      try {
        this.$confirm('确定要删除这个订单吗？删除后无法恢复！', '警告', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.$message.info('正在删除订单...')
          const { data } = await deleteApiOrder(row.order_id)
          this.$message.success(data || '订单删除成功')
          this.getList()
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除订单失败')
        }
      }
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pagination.limit = size
      this.pagination.page = 1
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.pagination.page = page
      this.getList()
    },

    // 获取支付方式标签
    getPaymentTypeTag(pay_type_name) {
      // 根据支付方式名称返回相应的标签类型
      const tagTypes = {
        '微信支付': 'success',
        '支付宝': 'primary',
        '小程序支付': 'warning',
        '微信APP': 'success',
        '支付宝APP': 'primary',
        '微信扫码': 'info',
        '支付宝扫码': 'info',
        '余额支付': 'warning',
        '线下支付': 'info',
        '银行卡支付': 'primary',
        '等待支付': 'danger',
        '未知支付': 'default',
        '其他支付': 'default'
      }
      return tagTypes[pay_type_name] || 'default'
    },

    // 复制文本
    copyText(text) {
      const input = document.createElement('input')
      input.value = text
      document.body.appendChild(input)
      input.select()
      document.execCommand('copy')
      document.body.removeChild(input)
      this.$message.success('文本已复制到剪贴板')
    },

    // 处理退款
    async handleRefund(row) {
      try {
        this.$confirm('确定要退款吗？此操作不可逆，将退还全部金额。', '退款确认', {
          confirmButtonText: '确定退款',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          this.$message.info('正在处理退款请求...')
          const { data } = await refundApiOrder(row.order_id)
          this.$message.success(data || '退款成功')
          // 关闭弹窗
          this.detailVisible = false
          // 重新加载数据
          this.getList()
        })
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退款失败:', error)
          this.$message.error('退款处理失败')
        }
      }
    },

    // 添加关闭详情方法
    closeDetail() {
      this.detailVisible = false
      // 刷新列表数据
      this.getList()
    },

    // 导出单个订单
    async handleExportSingle(row) {
      try {
        this.$message.info('正在导出订单，请稍候...')
        const { data } = await exportApiPaymentData({ order_id: row.order_id })
        
        // 检查返回的数据
        if (!data) {
          this.$message.error('导出失败 - 未获取到文件URL')
          return
        }
        
        // 直接打开下载链接
        const baseUrl = window.location.origin
        window.open(baseUrl + data, '_blank')
        
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped>
.api-payment {
  padding: 20px;
}

.mb-3 {
  margin-bottom: 20px;
}

.ml-2 {
  margin-left: 10px;
}

.mb-1 {
  margin-bottom: 4px;
}

.statistic-card {
  text-align: center;
  padding: 20px;
}

.statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: bold;
}

.amount {
  color: #E6A23C;
  font-weight: bold;
}

.merchant-search-box {
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: center;
  margin-top: 15px;
}

.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 90px;
  color: #99a9bf;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.copy-btn {
  color: #409EFF;
  cursor: pointer;
}

.copy-btn:hover {
  text-decoration: underline;
}

.text-muted {
  color: #909399;
}

.merchant-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.merchant-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.partner-code {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.refund-info {
  margin-top: 2px;
}

.refund-amount {
  color: #f56c6c;
  font-weight: bold;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.button-row {
  display: flex;
  gap: 5px;
}
</style> 