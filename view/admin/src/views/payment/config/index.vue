<template>
  <div class="divBox">
    <!-- 筛选卡片 -->
    <div class="selCard">
      <el-form ref="searchForm" :model="searchForm" :inline="true" class="demo-form-inline">
        <el-form-item label="关键词" prop="keyword">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入商户名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="searchForm.payment_type" placeholder="请选择支付方式" clearable style="width: 150px">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList(1)">查询</el-button>
          <el-button @click="searchReset">重置</el-button>
          <el-button type="success" @click="refreshData">刷新</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主要内容 -->
    <el-card class="mt14">
      <div class="mb20">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="商户配置" name="merchant"></el-tab-pane>
          <el-tab-pane label="灰度发布" name="gray"></el-tab-pane>
        </el-tabs>
        
        <div class="flex">
          <div class="flex-auto">
            <el-button v-if="activeTab !== 'gray'" size="small" type="primary" icon="el-icon-plus" @click="showAddDialog">
              添加配置
            </el-button>
            <el-button v-if="activeTab === 'gray'" size="small" type="primary" icon="el-icon-upload2" @click="showGrayReleaseDialog">
              发起灰度发布
            </el-button>
          </div>
          <div>
            <el-button size="small" type="info" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
          </div>
        </div>
      </div>

    
      <!-- 商户配置表格 -->
      <div v-show="activeTab === 'merchant'">
        <el-table v-loading="listLoading" :data="merchantConfigs" size="small">
          <el-table-column prop="mer_id" label="商户ID" min-width="100" />
          <el-table-column prop="merchant_name" label="商户名称" min-width="200">
            <template slot-scope="scope">
              {{ scope.row.merchant_name || '未知商户' }}
            </template>
          </el-table-column>
          <el-table-column prop="payment_type" label="支付方式" min-width="120">
            <template slot-scope="scope">
              <el-tag>{{ getPaymentTypeName(scope.row.payment_type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getConfigTypeTag(scope.row.config_type)">
                {{ getConfigTypeName(scope.row.config_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="is_service_mode" label="服务商模式" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="scope.row.is_service_mode ? 'warning' : 'info'">
                {{ scope.row.is_service_mode ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                {{ scope.row.status === 'active' ? '激活' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="220" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row">
                  <el-button type="info" size="small" icon="el-icon-view" @click="viewConfig(scope.row)">查看</el-button>
                  <el-button type="success" size="small" icon="el-icon-edit" @click="editConfig(scope.row)">编辑</el-button>
                </div>
                <div class="button-row">
                  <el-button type="primary" size="small" icon="el-icon-refresh" @click="hotUpdate(scope.row)">更新</el-button>
                  <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteConfig(scope.row)">删除</el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 灰度发布表格 -->
      <div v-show="activeTab === 'gray'">
        <el-table v-loading="listLoading" :data="grayReleases" size="small">
          <el-table-column prop="id" label="发布ID" min-width="100" />
          <el-table-column prop="config_type" label="配置类型" min-width="120">
            <template slot-scope="scope">
              <el-tag>商户</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="target_name" label="目标名称" min-width="200" />
          <el-table-column prop="status" label="状态" min-width="120">
            <template slot-scope="scope">
              <el-tag :type="getGrayStatusTag(scope.row.status)">
                {{ getGrayStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="gray_percent" label="灰度比例" min-width="120">
            <template slot-scope="scope">
              {{ scope.row.gray_percent }}%
            </template>
          </el-table-column>
          <el-table-column prop="operator_name" label="操作员" min-width="120" />
          <el-table-column prop="start_time" label="开始时间" min-width="180">
            <template slot-scope="scope">
              {{ formatTime(scope.row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" fixed="right">
            <template slot-scope="scope">
              <div class="button-group">
                <div class="button-row" v-if="scope.row.status === 'pending'" style="justify-content: center;">
                  <el-button
                    type="primary"
                    size="small"
                    icon="el-icon-video-play"
                    @click="executeGrayRelease(scope.row)"
                  >
                    执行
                  </el-button>
                </div>
                <div class="button-row" v-if="scope.row.status === 'running'" style="justify-content: center;">
                  <el-button
                    type="success"
                    size="small"
                    icon="el-icon-check"
                    @click="completeGrayRelease(scope.row)"
                  >
                    完成
                  </el-button>
                </div>
                <div class="button-row" v-if="['running', 'success'].includes(scope.row.status)" style="justify-content: center;">
                  <el-button
                    type="warning"
                    size="small"
                    icon="el-icon-back"
                    @click="rollbackGrayRelease(scope.row)"
                  >
                    回滚
                  </el-button>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="block">
        <el-pagination
          background
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </el-card>

    <!-- 配置编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="800px"
      @close="closeDialog"
    >
      <el-form :model="configForm" :rules="dynamicConfigRules" ref="configForm" label-width="120px">
        <el-form-item label="配置类型" prop="config_type">
          <el-select v-model="configForm.config_type" :disabled="editMode" class="w100">
            <el-option label="商户配置" value="merchant" />
          </el-select>
        </el-form-item>
        
        
        
        <el-form-item v-if="configForm.config_type === 'merchant'" label="商户" prop="mer_id">
          <!-- 编辑模式：显示商户名称+配置ID -->
          <template v-if="editMode">
            <el-input 
              :value="getMerchantDisplayText()" 
              :disabled="true" 
              class="w100"
              placeholder="商户信息"
            />
            <!-- 隐藏的配置ID字段 -->
            <el-input v-model="configForm.id" type="hidden" />
          </template>
          <!-- 新增模式：使用商户选择组件 -->
          <template v-else>
            <MerchantSelector
              v-model="configForm.mer_id"
              @change="handleMerchantChange"
            />
          </template>
        </el-form-item>
        
        <el-form-item label="支付方式" prop="payment_type">
          <el-select v-model="configForm.payment_type" class="w100" @change="onPaymentTypeChange">
            <el-option
              v-for="method in paymentMethods"
              :key="method.value"
              :label="method.label"
              :value="method.value"
            />
          </el-select>
        </el-form-item>

        <!-- 支付模式选择 -->
        <el-form-item v-if="availableModes.length > 0" label="支付模式" prop="payment_mode">
          <el-select v-model="selectedMode" class="w100" @change="onModeChange">
            <el-option
              v-for="mode in availableModes"
              :key="mode.value"
              :label="mode.label"
              :value="mode.value"
            >
              <span style="float: left">{{ mode.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ mode.description }}</span>
            </el-option>
          </el-select>
          <div class="form-tip">
            不同模式的配置字段和使用场景不同
          </div>
        </el-form-item>

        <!-- 接口版本选择 -->
        <el-form-item v-if="availableVersions.length > 0" label="接口版本" prop="payment_version">
          <el-select v-model="selectedVersion" class="w100" @change="onVersionChange">
            <el-option
              v-for="version in availableVersions"
              :key="version.value"
              :label="version.label"
              :value="version.value"
            >
              <span style="float: left">{{ version.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ version.description }}</span>
            </el-option>
          </el-select>
          <div class="form-tip">
            不同版本的接口字段和验签方式不同
          </div>
        </el-form-item>



        <!-- 动态配置字段 -->
        <div v-if="currentFields.length > 0">
          <!-- 当前配置信息 -->
          <div class="config-info mb20">
            <el-alert
              :title="getCurrentConfigTitle()"
              type="info"
              :closable="false"
              show-icon>
              <template slot="description">
                <p>{{ getCurrentConfigDescription() }}</p>
              </template>
            </el-alert>
          </div>

          <!-- 配置字段 -->
          <div v-for="fieldName in currentFields" :key="fieldName">
            <el-form-item
              :label="getFieldLabel(fieldName)"
              :prop="`config.${fieldName}`"
              :required="isFieldRequired(fieldName)"
            >
              <el-input
                v-model="configForm.config[fieldName]"
                :placeholder="`请输入${getFieldLabel(fieldName)}`"
                :type="isSecretField(fieldName) ? 'password' : 'text'"
                :show-password="isSecretField(fieldName)"
              />
              <div v-if="getFieldTip(fieldName)" class="form-tip">
                {{ getFieldTip(fieldName) }}
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 模板加载状态 -->
        <div v-else-if="configForm.payment_type" class="template-loading">
          <el-alert
            title="正在加载配置模板..."
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
        
        <el-form-item label="状态">
          <el-switch
            v-model="configForm.status"
            active-value="active"
            inactive-value="disabled"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button icon="el-icon-close" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" icon="el-icon-check" @click="saveConfig">保存</el-button>
      </div>
    </el-dialog>

    <!-- 热更新对话框 -->
    <el-dialog
      title="配置热更新"
      :visible.sync="hotUpdateDialogVisible"
      width="600px"
    >
      <el-form :model="hotUpdateForm" label-width="120px">
        <el-form-item label="更新说明">
          <el-input
            v-model="hotUpdateForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入更新说明"
          />
        </el-form-item>
        <el-form-item label="更新方式">
          <el-radio-group v-model="hotUpdateForm.update_type">
            <el-radio label="immediate">立即更新</el-radio>
            <el-radio label="gray">灰度更新</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="hotUpdateForm.update_type === 'gray'" label="灰度比例">
          <el-slider
            v-model="hotUpdateForm.gray_percent"
            :min="1"
            :max="100"
            show-stops
            :marks="{10: '10%', 50: '50%', 100: '100%'}"
          />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="text-center">
        <el-button @click="hotUpdateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmHotUpdate">确认更新</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  getMerchantConfigsApi,
  getGrayReleasesApi,
  createPaymentConfigApi,
  updatePaymentConfigApi,
  deletePaymentConfigApi,
  hotUpdateConfigApi,
  grayReleaseActionApi,
  getPaymentConfigApi,
  getMerchantListApi,
  getConfigPaymentMethodsApi,
  getConfigTemplateApi
} from '@/api/payment'
import MerchantSelector from '@/components/MerchantSelector.vue'

export default {
  name: 'PaymentConfig',
  components: {
    MerchantSelector
  },
  data() {
    return {
      activeTab: 'merchant',
      listLoading: false,
      searchForm: {
        config_type: '',
        payment_type: '',
        keyword: ''
      },
      merchantConfigs: [],
      grayReleases: [],
      merchantList: [],
      paymentMethods: [], // 动态支付方式列表
      configTemplate: null, // 配置模板
      modeInfo: null, // 支付模式信息
      availableModes: [], // 可用的支付模式
      selectedPaymentMode: null, // 用户选择的支付模式
      // 支付配置联动映射
      paymentFieldMapping: {
        // 微信支付
        wechat: {
          modes: [
            { value: 'direct', label: '直连模式', description: '商户直接对接微信支付' },
            { value: 'service', label: '服务商模式', description: '通过服务商代理支付' },
            { value: 'ecommerce', label: '电商收付通', description: '电商平台二级商户' }
          ],
          versions: {
            direct: [
              { value: 'v2', label: 'API V2版本', description: '使用V2接口，需要API密钥' },
              { value: 'v3', label: 'API V3版本', description: '使用V3接口，需要证书（推荐）' }
            ],
            service: [
              { value: 'v3', label: 'API V3版本', description: '服务商模式仅支持V3接口' }
            ],
            ecommerce: [
              { value: 'v3', label: 'API V3版本', description: '电商收付通仅支持V3接口' }
            ]
          },
          fields: {
            'direct_v2': ['appid', 'mch_id', 'key'],
            'direct_v3': ['appid', 'mch_id', 'api_v3_key', 'serial_no', 'private_key'],
            'service_v3': ['appid', 'mch_id', 'service_appid', 'service_mch_id', 'service_v3_key', 'service_serial_no', 'service_private_key'],
            'ecommerce_v3': ['appid', 'sub_mch_id', 'service_appid', 'service_mch_id', 'service_v3_key', 'service_serial_no', 'service_private_key']
          }
        },
        // 支付宝
        alipay: {
          modes: [
            { value: 'direct', label: '直连模式', description: '商户直接对接支付宝' },
            { value: 'service', label: '服务商模式', description: '通过服务商代理支付' },
            { value: 'zhifu', label: '直付通', description: '支付宝直付通模式' }
          ],
          versions: {
            direct: [
              { value: 'normal', label: '普通模式', description: '使用密钥验签' },
              { value: 'cert', label: '证书模式', description: '使用证书验签（推荐）' }
            ],
            service: [
              { value: 'normal', label: '普通模式', description: '使用密钥验签' },
              { value: 'cert', label: '证书模式', description: '使用证书验签（推荐）' }
            ],
            zhifu: [
              { value: 'normal', label: '普通模式', description: '直付通模式' }
            ]
          },
          fields: {
            'direct_normal': ['app_id', 'private_key', 'public_key'],
            'direct_cert': ['app_id', 'private_key', 'app_cert', 'public_cert', 'root_cert'],
            'service_normal': ['app_id', 'private_key', 'service_app_id', 'service_private_key', 'service_public_key'],
            'service_cert': ['app_id', 'private_key', 'service_app_id', 'service_private_key', 'service_app_cert', 'service_public_cert', 'service_root_cert'],
            'zhifu_normal': ['app_id', 'private_key', 'service_app_id', 'service_private_key', 'alipay_smid', 'app_auth_token']
          }
        },
        // 其他支付方式
        balance: {
          modes: [{ value: 'direct', label: '余额支付', description: '使用用户余额支付' }],
          versions: { direct: [{ value: 'default', label: '默认', description: '余额支付' }] },
          fields: { 'direct_default': [] }
        },
        offline: {
          modes: [{ value: 'direct', label: '线下支付', description: '线下收款' }],
          versions: { direct: [{ value: 'default', label: '默认', description: '线下支付' }] },
          fields: { 'direct_default': [] }
        }
      },
      selectedMode: null, // 选择的支付模式
      selectedVersion: null, // 选择的版本
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20
      },
      dialogVisible: false,
      hotUpdateDialogVisible: false,
      editMode: false,
      dialogTitle: '新增配置',
      configForm: {
        id: null,
        config_type: 'merchant',
        mer_id: '',
        payment_type: 'wechat',
        config: {}, // 动态配置字段
        status: 'active'
      },
      hotUpdateForm: {
        description: '',
        update_type: 'immediate',
        gray_percent: 10
      }
    }
  },
  computed: {
    // 动态表单验证规则
    dynamicConfigRules() {
      const baseRules = {
        config_type: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ],
        payment_type: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ]
      }

      // 根据配置类型添加相应的验证规则
      if (this.configForm.config_type === 'merchant') {
        baseRules.mer_id = [
          { required: true, message: '请选择商户', trigger: 'change' }
        ]
      }

      return baseRules
    },

    // 可用的支付模式
    availableModes() {
      const paymentType = this.configForm.payment_type
      if (!paymentType || !this.paymentFieldMapping[paymentType]) {
        return []
      }
      return this.paymentFieldMapping[paymentType].modes || []
    },

    // 可用的接口版本
    availableVersions() {
      const paymentType = this.configForm.payment_type
      const mode = this.selectedMode
      if (!paymentType || !mode || !this.paymentFieldMapping[paymentType]) {
        return []
      }
      return this.paymentFieldMapping[paymentType].versions[mode] || []
    },

    // 当前需要显示的字段
    currentFields() {
      const paymentType = this.configForm.payment_type
      const mode = this.selectedMode
      const version = this.selectedVersion

      if (!paymentType || !mode || !version || !this.paymentFieldMapping[paymentType]) {
        return []
      }

      const fieldKey = `${mode}_${version}`
      return this.paymentFieldMapping[paymentType].fields[fieldKey] || []
    }
  },
  mounted() {
    this.getList()
    this.loadPaymentMethods()
    this.loadMerchantList()
  },
  methods: {
     
    // 加载支付方式列表
    async loadPaymentMethods() {
      try {
        const res = await getConfigPaymentMethodsApi()
        // 后端直接返回数组，不需要.list
        this.paymentMethods = res.data || []

      } catch (error) {
        console.error('获取支付方式失败:', error)
        // 如果获取失败，使用默认支付方式
        this.paymentMethods = [
          { value: 'wechat', label: '微信支付' },
          { value: 'alipay', label: '支付宝' },
          { value: 'routine', label: '小程序支付' },
          { value: 'balance', label: '余额支付' },
          { value: 'offline', label: '线下支付' }
        ]
      }
    },

    // 加载商户列表
    async loadMerchantList() {
      try {
        const res = await getMerchantListApi()
        this.merchantList = res.data || []

      } catch (error) {
        console.error('获取商户列表失败:', error)
        this.merchantList = []
      }
    },

    // 商户变化处理
    handleMerchantChange(merchant) {
      // 商户选择后可以重新加载支付配置
      console.log('选择商户:', merchant)
    },

    // 支付模式变化处理
    onModeChange() {
      this.selectedVersion = null
      this.configForm.config = {}

      // 自动选择第一个版本
      if (this.availableVersions.length > 0) {
        this.selectedVersion = this.availableVersions[0].value
        this.onVersionChange()
      }
    },

    // 版本变化处理
    onVersionChange() {
      // 重置配置，保留已有值
      const newConfig = {}
      this.currentFields.forEach(fieldName => {
        newConfig[fieldName] = this.configForm.config[fieldName] || ''
      })
      this.configForm.config = newConfig
    },

    // 获取当前配置标题
    getCurrentConfigTitle() {
      if (!this.selectedMode || !this.selectedVersion) {
        return '请选择支付模式和版本'
      }

      const paymentType = this.configForm.payment_type
      const modeObj = this.availableModes.find(m => m.value === this.selectedMode)
      const versionObj = this.availableVersions.find(v => v.value === this.selectedVersion)
      const modeLabel = modeObj ? modeObj.label : this.selectedMode
      const versionLabel = versionObj ? versionObj.label : this.selectedVersion

      return `${this.getPaymentTypeName(paymentType)} - ${modeLabel} - ${versionLabel}`
    },

    // 获取当前配置描述
    getCurrentConfigDescription() {
      if (!this.selectedMode || !this.selectedVersion) {
        return '选择支付模式和版本后将显示对应的配置字段'
      }

      const mode = this.availableModes.find(m => m.value === this.selectedMode)
      const version = this.availableVersions.find(v => v.value === this.selectedVersion)
      const modeDesc = mode ? mode.description : ''
      const versionDesc = version ? version.description : ''

      return `${modeDesc} - ${versionDesc}`
    },
    // 重置搜索
    searchReset() {
      // 安全地重置搜索表单
      if (this.$refs.searchForm) {
        try {
          this.$refs.searchForm.resetFields()
        } catch (error) {
          console.warn('搜索表单重置失败，手动重置:', error)
          // 手动重置搜索表单
          this.searchForm = {
            config_type: '',
            payment_type: '',
            keyword: ''
          }
        }
      } else {
        // 如果表单引用不存在，手动重置
        this.searchForm = {
          config_type: '',
          payment_type: '',
          keyword: ''
        }
      }
      this.getList(1)
    },
    // 标签页切换
    handleTabClick() {
      this.tableFrom.page = 1
      // 清空搜索条件，因为不同tab的搜索字段可能不同
      this.searchForm = {
        config_type: '',
        payment_type: '',
        keyword: ''
      }
      this.getList()
    },
    // 获取列表数据
    async getList(num) {
      this.listLoading = true
      this.tableFrom.page = num ? num : this.tableFrom.page
      
      try {
        if (this.activeTab === 'merchant') {
          const res = await getMerchantConfigsApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit,
            ...this.searchForm
          })
          this.merchantConfigs = res.data.list
          this.tableData.total = res.data.total
        } else if (this.activeTab === 'gray') {
          const res = await getGrayReleasesApi({
            page: this.tableFrom.page,
            limit: this.tableFrom.limit
          })
          this.grayReleases = res.data.list
          this.tableData.total = res.data.total
        }
      } catch (error) {
        this.$message.error('获取数据失败')
      } finally {
        this.listLoading = false
      }
    },
    // 刷新数据
    refreshData() {
      this.getList()
      this.$message.success('数据已刷新')
    },
    // 分页处理
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    // 显示新增对话框
    async showAddDialog() {
      this.editMode = false
      this.dialogTitle = '新增配置'
      this.resetForm()
      this.configForm.config_type = this.activeTab

      // 新增时立即获取默认支付类型的模板
      await this.onPaymentTypeChange()

      this.dialogVisible = true
    },
    // 编辑配置
    async editConfig(row) {
      this.editMode = true
      this.dialogTitle = '编辑配置'
      
      try {
        // 根据配置类型获取完整的配置详情
        let configData = { ...row }
        
        if (this.activeTab === 'merchant') {
          const res = await getPaymentConfigApi({
            mer_id: row.mer_id,
            payment_type: row.payment_type
          })

          configData = {
            id: row.id,
            config_type: 'merchant',
            mer_id: row.mer_id,
            merchant_name: row.merchant_name,
            payment_type: row.payment_type,
            config: res.data.config || {},
            status: row.status || 'active'
          }

          // 设置配置模板
          this.configTemplate = res.data.template || {}
        }
        
        this.configForm = configData

        // 编辑模式需要获取可用模式和设置当前模式
        await this.loadAvailableModesForEdit()

        this.dialogVisible = true


        
      } catch (error) {
        console.error('获取配置详情失败:', error)
        // 如果获取详情失败，使用基本数据
        this.configForm = {
          id: row.id,
          config_type: this.activeTab,
          mer_id: row.mer_id || '',
          merchant_name: row.merchant_name || '未知商户',
          payment_type: row.payment_type || 'wechat',
          config: {},
          status: row.status || 'active'
        }
        this.configTemplate = null
        this.dialogVisible = true
        this.$message.warning('获取配置详情失败，使用基本信息编辑')
      }
    },
    // 查看配置
    viewConfig(row) {
      this.editMode = true
      this.dialogTitle = '查看配置'
      this.configForm = { ...row }
      this.dialogVisible = true
    },
    // 保存配置
    async saveConfig() {
      try {
        await this.$refs.configForm.validate()

        // 构建支付模式
        let paymentMode = null
        if (this.selectedMode && this.selectedVersion) {
          paymentMode = `${this.configForm.payment_type}_${this.selectedMode}_${this.selectedVersion}`
        }

        const configData = {
          mer_id: this.configForm.mer_id,
          payment_type: this.configForm.payment_type,
          config: this.configForm.config,
          status: this.configForm.status,
          payment_mode: paymentMode // 发送构建的支付模式
        }

        if (this.editMode && this.configForm.id) {
          // 编辑模式：更新配置
          await updatePaymentConfigApi(configData)
          this.$message.success('配置更新成功')
        } else {
          // 新增模式：创建配置
          await createPaymentConfigApi(configData)
          this.$message.success('配置创建成功')
        }

        this.dialogVisible = false
        this.getList()
      } catch (error) {
        console.error('配置保存失败:', error)
        this.$message.error('配置保存失败: ' + (error.message || error))
      }
    },
    // 热更新
    hotUpdate(row) {
      this.selectedConfig = row
      this.hotUpdateDialogVisible = true
    },
    // 确认热更新
    async confirmHotUpdate() {
      try {
        const data = {
          config_id: this.selectedConfig.id,
          config_type: this.selectedConfig.config_type || this.activeTab,
          ...this.hotUpdateForm
        }
        await hotUpdateConfigApi(data)
        this.$message.success('配置热更新成功')
        this.hotUpdateDialogVisible = false
        this.getList()
      } catch (error) {
        this.$message.error('配置热更新失败')
      }
    },
    // 删除配置
    async deleteConfig(row) {
      try {
        await this.$confirm('确认删除此配置？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 删除配置API调用
        const configType = row.config_type || this.activeTab
        // 重要：使用配置记录ID，不是merchant_id
        const configId = row.id // 这是eb_merchant_payment_config.id
        

        
        await deletePaymentConfigApi(configType, configId)
        this.$message.success('配置删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          this.$message.error('配置删除失败: ' + (error.message || error))
        }
      }
    },
    // 显示灰度发布对话框
    showGrayReleaseDialog() {
      this.$message.info('灰度发布功能开发中')
    },
    // 执行灰度发布
    async executeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'execute')
        this.$message.success('灰度发布执行成功')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布执行失败')
      }
    },
    // 完成灰度发布
    async completeGrayRelease(row) {
      try {
        await grayReleaseActionApi(row.id, 'complete')
        this.$message.success('灰度发布完成')
        this.getList()
      } catch (error) {
        this.$message.error('灰度发布完成失败')
      }
    },
    // 回滚灰度发布
    async rollbackGrayRelease(row) {
      try {
        await this.$confirm('确认回滚此灰度发布？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await grayReleaseActionApi(row.id, 'rollback')
        this.$message.success('灰度发布回滚成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('灰度发布回滚失败')
        }
      }
    },
    // 重置表单
    resetForm() {
      // 重置为简化的表单结构
      this.configForm = {
        id: null,
        config_type: 'merchant',
        mer_id: '',
        payment_type: 'wechat',
        config: {},
        status: 'active'
      }

      // 清空配置模板和模式信息
      this.selectedMode = null
      this.selectedVersion = null

      // 在下一个tick中安全地重置表单验证状态
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          try {
            this.$refs.configForm.clearValidate()
          } catch (error) {
            console.warn('表单验证状态清除失败:', error)
          }
        }
      })
    },
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
      this.resetForm()
    },
    // 时间格式化
    formatTime(timestamp) {
      if (!timestamp) return '-'
      return new Date(timestamp * 1000).toLocaleString()
    },
    // 获取支付方式名称
    getPaymentTypeName(type) {
      const names = {
        wechat: '微信支付',
        alipay: '支付宝',
        routine: '小程序',
        weixinApp: '微信APP',
        alipayApp: '支付宝APP',
        weixinQr: '微信扫码',
        alipayQr: '支付宝扫码',
        alipayBarCode: '支付宝付款码'
      }
      return names[type] || type
    },
    // 获取配置类型标签
    getConfigTypeTag(type) {
      const tags = {      
        merchant_alipay_auth: 'success',
        merchant_config: 'warning',
        system_config: 'danger'
      }
      return tags[type] || 'default'
    },
    // 获取配置类型名称
    getConfigTypeName(type) {
      const names = {
        merchant_alipay_auth: '商户授权',
        merchant_config: '商户配置',
        system_config: '系统配置'
      }
      return names[type] || type
    },
    // 获取灰度状态标签
    getGrayStatusTag(status) {
      const tags = {
        pending: 'info',
        running: 'warning',
        success: 'success',
        failed: 'danger',
        rollback: 'primary'
      }
      return tags[status] || 'default'
    },
    // 获取灰度状态名称
    getGrayStatusName(status) {
      const names = {
        pending: '待执行',
        running: '进行中',
        success: '成功',
        failed: '失败',
        rollback: '已回滚'
      }
      return names[status] || status
    },
    // 支付方式变化处理
    onPaymentTypeChange() {
      // 重置相关状态
      this.selectedMode = null
      this.selectedVersion = null
      this.configForm.config = {}

      if (!this.configForm.payment_type) return

      // 自动选择第一个模式
      if (this.availableModes.length > 0) {
        this.selectedMode = this.availableModes[0].value
        this.onModeChange()
      }
    },

    // 子类型变化处理
    async onSubTypeChange() {
      if (!this.selectedSubType) return
      await this.loadPaymentTemplate()
    },

    // 加载支付模板
    async loadPaymentTemplate() {
      try {
        // 构建支付模式参数
        let paymentMode = null
        if (this.selectedSubType) {
          // 根据支付方式和子类型构建模式
          if (this.configForm.payment_type === 'wechat') {
            paymentMode = this.selectedSubType === 'v2' ? 'wechat_direct_v2' : 'wechat_direct_v3'
          } else if (this.configForm.payment_type === 'alipay') {
            paymentMode = this.selectedSubType === 'cert' ? 'alipay_direct_cert' : 'alipay_direct_normal'
          }
        }

        // 使用专门的模板API获取配置模板和模式信息
        const res = await getConfigTemplateApi({
          payment_type: this.configForm.payment_type,
          mer_id: this.configForm.mer_id || 0,
          payment_mode: paymentMode
        })

        this.availableModes = res.data.available_modes || []

        // 如果只有一种模式，直接使用；如果有多种模式，让用户选择
        if (this.availableModes.length === 1) {
          this.selectedPaymentMode = this.availableModes[0].mode
          await this.loadTemplateForMode(this.selectedPaymentMode)
        } else if (this.availableModes.length > 1) {
          // 默认选择第一个模式
          this.selectedPaymentMode = this.availableModes[0].mode
          await this.loadTemplateForMode(this.selectedPaymentMode)
        } else {
          // 没有可用模式，清空模板
          this.configTemplate = {}
          this.modeInfo = null
        }

      } catch (error) {
        console.error('获取配置模板失败:', error)
        this.$message.warning('获取配置模板失败，请重试')
      }
    },

    // 支付模式变化处理
    async onPaymentModeChange() {
      if (!this.selectedPaymentMode) return
      await this.loadTemplateForMode(this.selectedPaymentMode)
    },

    // 加载指定模式的模板
    async loadTemplateForMode(paymentMode) {
      try {
        const res = await getConfigTemplateApi({
          payment_type: this.configForm.payment_type,
          mer_id: this.configForm.mer_id || 0,
          payment_mode: paymentMode
        })

        this.configTemplate = res.data.template || {}
        this.modeInfo = res.data.mode_info || null

        // 初始化配置字段，保留已有值
        const newConfig = {}
        Object.keys(this.configTemplate).forEach(fieldName => {
          // 保留已有值，否则使用空字符串
          newConfig[fieldName] = this.configForm.config[fieldName] || ''
        })
        this.configForm.config = newConfig


      } catch (error) {
        console.error('加载模式模板失败:', error)
        this.$message.warning('加载模式模板失败，请重试')
      }
    },

    // 编辑模式加载可用模式
    async loadAvailableModesForEdit() {
      try {
        const res = await getConfigTemplateApi({
          payment_type: this.configForm.payment_type,
          mer_id: this.configForm.mer_id || 0
        })

        this.availableModes = res.data.available_modes || []
        this.configTemplate = res.data.template || {}
        this.modeInfo = res.data.mode_info || null

        // 根据当前配置推断支付模式
        if (this.availableModes.length > 0) {
          // 如果只有一种模式，直接使用
          if (this.availableModes.length === 1) {
            this.selectedPaymentMode = this.availableModes[0].mode
          } else {
            // 多种模式时，尝试根据配置推断当前模式
            this.selectedPaymentMode = this.inferPaymentModeFromConfig()
          }
        }


      } catch (error) {
        console.error('加载编辑模式失败:', error)
        this.$message.warning('加载编辑模式失败，请重试')
      }
    },

    // 根据配置推断支付模式
    inferPaymentModeFromConfig() {
      const config = this.configForm.config || {}

      // 根据配置字段的存在情况推断模式
      if (this.configForm.payment_type.includes('wechat') || this.configForm.payment_type.includes('weixin')) {
        // 如果有服务商相关字段，推断为服务商模式
        if (config.service_mchid || config.service_key || config.service_v3_key) {
          return 'wechat_service'
        }
        // 如果有子商户号，推断为电商收付通
        if (config.sub_mchid) {
          return 'wechat_ecommerce'
        }
        // 默认直连模式
        return 'wechat_direct'
      } else if (this.configForm.payment_type.includes('alipay')) {
        // 如果有服务商相关字段，推断为服务商模式
        if (config.service_app_id || config.service_private_key) {
          return 'alipay_service'
        }
        // 如果有SMID，推断为直付通
        if (config.alipay_smid) {
          return 'alipay_zhifu'
        }
        // 默认直连模式
        return 'alipay_direct'
      }

      // 默认返回第一个可用模式
      return this.availableModes.length > 0 ? this.availableModes[0].mode : null
    },

    // 判断是否应该显示某个层级
    shouldShowLayer(layerName) {
      if (!this.modeInfo) return true

      // 总是显示基础层
      if (layerName === 'base') return true

      // 服务商模式才显示服务商层
      if (layerName === 'service') {
        return this.modeInfo.is_service_mode
      }

      // 商户层通常不需要用户配置（由系统自动管理）
      if (layerName === 'merchant') return false

      return true
    },

    // 获取商户名称
    getMerchantName(merchantId) {
      const merchant = this.merchantList.find(m => m.mer_id === merchantId)
      return merchant ? merchant.mer_name : '未知商户'
    },
    // 获取商户显示文本
    getMerchantDisplayText() {
      // 优先使用保存在configForm中的商户名称（来自后端JOIN查询）
      const merchantName = this.configForm.merchant_name || '未知商户'
      const configId = this.configForm.id || '新配置'
      return `${merchantName} (配置ID: ${configId})`
    },

    // 获取层级标签类型
    getLayerTagType(layer) {
      const typeMap = {
        'base': 'primary',
        'service': 'warning',
        'merchant': 'success'
      }
      return typeMap[layer] || 'info'
    },

    // 获取层级名称
    getLayerName(layer) {
      const nameMap = {
        'base': '基础配置',
        'service': '服务商配置',
        'merchant': '商户配置'
      }
      return nameMap[layer] || layer
    },

    // 判断是否为敏感字段
    isSecretField(fieldName) {
      const secretFields = [
        // 基础密钥字段
        'key', 'private_key', 'api_v3_key',
        // 服务商密钥字段
        'service_key', 'service_v3_key', 'service_private_key',
        // 支付宝密钥字段
        'service_private_key',
        // Token字段
        'app_auth_token', 'refresh_token'
      ]
      return secretFields.includes(fieldName)
    },

    // 获取字段标签
    getFieldLabel(fieldName) {
      const fieldLabels = {
        // 微信支付字段
        'appid': '应用ID',
        'mch_id': '商户号',
        'key': 'API密钥',
        'api_v3_key': 'APIv3密钥',
        'serial_no': '证书序列号',
        'private_key': '商户私钥',
        'service_appid': '服务商应用ID',
        'service_mch_id': '服务商商户号',
        'service_key': '服务商API密钥',
        'service_v3_key': '服务商APIv3密钥',
        'service_serial_no': '服务商证书序列号',
        'service_private_key': '服务商私钥',
        'sub_mch_id': '子商户号',

        // 支付宝字段
        'app_id': '应用ID',
        'public_key': '支付宝公钥',
        'app_cert': '应用证书',
        'public_cert': '支付宝公钥证书',
        'root_cert': '根证书',
        'service_app_id': '服务商应用ID',
        'service_private_key': '服务商私钥',
        'service_public_key': '服务商公钥',
        'service_app_cert': '服务商应用证书',
        'service_public_cert': '服务商公钥证书',
        'service_root_cert': '服务商根证书',
        'alipay_smid': '支付宝商户ID',
        'app_auth_token': '应用授权令牌'
      }
      return fieldLabels[fieldName] || fieldName
    },

    // 获取字段提示
    getFieldTip(fieldName) {
      const fieldTips = {
        'appid': '微信开放平台或公众平台的应用ID',
        'mch_id': '微信支付商户号',
        'key': '微信支付API密钥，在商户平台设置',
        'api_v3_key': '微信支付APIv3密钥，在商户平台设置',
        'serial_no': '商户证书序列号，从证书中获取',
        'private_key': '商户私钥文件内容，PEM格式',
        'service_appid': '服务商的微信应用ID',
        'service_mch_id': '服务商的微信支付商户号',
        'sub_mch_id': '子商户号，由服务商分配',

        'app_id': '支付宝开放平台的应用ID',
        'private_key': '应用私钥，RSA2格式',
        'public_key': '支付宝公钥，从开放平台获取',
        'app_cert': '应用证书文件内容',
        'public_cert': '支付宝公钥证书文件内容',
        'root_cert': '根证书文件内容'
      }
      return fieldTips[fieldName] || ''
    },

    // 字段是否必填
    isFieldRequired(fieldName) {
      // 所有字段都是必填的
      return true
    },

    // 获取支付方式名称
    getPaymentTypeName(paymentType) {
      const names = {
        'wechat': '微信支付',
        'alipay': '支付宝',
        'balance': '余额支付',
        'offline': '线下支付'
      }
      return names[paymentType] || paymentType
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  background-color: #f5f7f9;
  padding: 14px;
}

.selCard {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
}

.selWidth {
  width: 200px;
}

.w100 {
  width: 100%;
}

.text-gray {
  color: #999;
}

.ml10 {
  margin-left: 10px;
}

.fee-rate {
  color: #e6a23c;
  font-weight: bold;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  padding: 5px 0;
}

.button-row {
  display: flex;
  justify-content: center;
  gap: 10px;
  width: 100%;
}

.mt14 {
  margin-top: 14px;
}

.mb20 {
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

.field-header {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}

.required-mark {
  color: #f56c6c;
  font-weight: bold;
}

.template-loading {
  margin: 20px 0;
}

.mb5 {
  margin-bottom: 5px;
}

.merchant-search-box {
  margin-bottom: 15px;
}
</style> 